<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />

<title>{{ $title ?? config('app.name') }}</title>

<link rel="icon" href="/favicon.ico" sizes="any">
<link rel="icon" href="/favicon.svg" type="image/svg+xml">
<link rel="apple-touch-icon" href="/apple-touch-icon.png">

<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

<style>
/* Inline CSS for immediate styling */
body {
    font-family: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(to bottom, #0a0a0a, #171717);
    color: white;
}

.min-h-svh {
    min-height: 100vh;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.gap-6 {
    gap: 1.5rem;
}

.gap-2 {
    gap: 0.5rem;
}

.p-6 {
    padding: 1.5rem;
}

.w-full {
    width: 100%;
}

.max-w-sm {
    max-width: 24rem;
}

.h-9 {
    height: 2.25rem;
}

.w-9 {
    width: 2.25rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.font-medium {
    font-weight: 500;
}

.text-center {
    text-align: center;
}

.text-sm {
    font-size: 0.875rem;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Form styles */
input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    background-color: #374151;
    color: white;
    font-size: 0.875rem;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.1);
}

label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #d1d5db;
    margin-bottom: 0.5rem;
}

button {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: white;
    color: black;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s;
}

button:hover {
    background-color: #f3f4f6;
}

a {
    color: #60a5fa;
    text-decoration: underline;
}

a:hover {
    color: #93c5fd;
}

h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

p {
    color: #9ca3af;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

.space-x-1 > * + * {
    margin-left: 0.25rem;
}

.text-zinc-600 {
    color: #52525b;
}

.text-zinc-400 {
    color: #a1a1aa;
}

.justify-end {
    justify-content: flex-end;
}
</style>
@fluxAppearance
